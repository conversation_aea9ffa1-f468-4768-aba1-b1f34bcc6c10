/* Today Entries Component Styles */

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.spinner-container {
  text-align: center;
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 2rem;
}

.visitor-summary-card {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  text-align: center;
  padding: 1.5rem 2rem 1rem 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.15);
  transition: all 0.3s ease;
}

.visitor-summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(67, 97, 238, 0.2);
}

.search-result-card {
  background: linear-gradient(135deg, var(--success) 0%, var(--success-dark) 100%);
  color: white;
  text-align: center;
  padding: 1.5rem 2rem 1rem 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 20px rgba(40, 167, 69, 0.15);
  transition: all 0.3s ease;
}

.search-result-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(40, 167, 69, 0.2);
}

.visitor-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem;
  font-size: 1.75rem;
}

.search-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem;
  font-size: 1.75rem;
}

.visitor-count {
  font-size: 2.75rem;
  font-weight: 700;
  margin: 0.5rem 0;
}

.search-info {
  font-size: 1.1rem;
  margin: 0.5rem 0;
  opacity: 0.9;
}

.result-count {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0.5rem 0;
}

.date-display {
  font-size: 1rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

/* Modern Stats Cards */
.modern-stats-card {
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  height: 100%;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.modern-stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modern-stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
  background-color: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.modern-stats-info {
  flex-grow: 1;
}

.modern-stats-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.modern-stats-label {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.modern-stats-subtext {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Background Gradients */
.bg-primary-gradient {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
}

.bg-success-gradient {
  background: linear-gradient(135deg, #28a745 0%, #208838 100%);
  color: white;
}

.bg-info-gradient {
  background: linear-gradient(135deg, #4cc9f0 0%, #4895ef 100%);
  color: white;
}

/* Chart Container */
.chart-container {
  height: 300px;
  margin-top: 2rem;
  position: relative;
}

/* Member Avatar */
.member-info {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

/* Table Styles */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table th {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.modern-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr {
  transition: background-color 0.3s ease;
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
}

.modern-table tbody tr.active-entry {
  background-color: rgba(40, 167, 69, 0.05);
}

/* Empty State */
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: var(--text-secondary);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.zoom-in {
  animation: zoomIn 0.5s ease-out;
}

@keyframes zoomIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* Dark Mode Support */
[data-theme="dark"] .loading-overlay {
  background-color: rgba(18, 18, 18, 0.8);
}

[data-theme="dark"] .modern-table th {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-table tbody tr.active-entry {
  background-color: rgba(40, 167, 69, 0.1);
}

/* Form controls and labels styling for both light and dark modes */
.form-label, .modern-form-label {
  color: #888888 !important; /* Gray color visible in both modes */
  font-weight: bold;
}

input::placeholder,
textarea::placeholder,
.form-control::placeholder,
.search-input::placeholder {
  color: #888888 !important; /* Gray color visible in both modes */
  opacity: 0.9 !important;
}

/* Specific styling for the search input in today-entries */
.search-container .search-input::placeholder {
  color: #888888 !important; /* Gray color visible in both modes */
  opacity: 0.9 !important;
}

/* Ensure form controls have proper contrast in dark mode */
[data-theme="dark"] .form-control {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

/* Search Button */
.search-btn {
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  font-size: 0.9rem;
}

/* Custom Styles for Today Entries */
.form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .visitor-count {
    font-size: 2.5rem;
  }

  .visitor-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .search-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .modern-stats-card {
    margin-bottom: 1rem;
  }

  .modern-stats-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .modern-stats-value {
    font-size: 1.5rem;
  }

  .chart-container {
    height: 250px;
  }

  .table-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  .table-actions button {
    flex: 1;
  }

  .pagination-container {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .pagination-info {
    text-align: center;
  }

  .pagination-controls {
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
  }
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem 0;
  border-top: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 1rem;
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-size-selector .form-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  white-space: nowrap;
  margin-bottom: 0;
}

.page-size-selector .form-select {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.page-size-selector .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem var(--primary-light);
  outline: none;
}

/* Pagination Navigation Styles */
.pagination {
  margin-bottom: 0;
  display: flex;
  gap: 0.35rem;
  list-style: none;
  padding: 0;
}

.pagination .page-item {
  display: flex;
}

.pagination .page-link {
  border: none;
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
  padding: 0.6rem 0.9rem;
  margin: 0;
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  cursor: pointer;
}

.pagination .page-link:hover {
  background-color: var(--primary-light);
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

.pagination .page-item.active .page-link {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3);
}

.pagination .page-item.disabled .page-link {
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
  opacity: 0.5;
  box-shadow: none;
  cursor: not-allowed;
  transform: none;
}

.pagination .page-item.disabled .page-link:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  transform: none;
  box-shadow: none;
}

/* Dark mode specific adjustments */
[data-theme="dark"] .pagination .page-link {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .pagination .page-link:hover {
  background-color: var(--primary-light);
  color: var(--primary);
  border-color: var(--primary);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .pagination .page-item.active .page-link {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  border-color: var(--primary);
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.4);
}

[data-theme="dark"] .pagination .page-item.disabled .page-link {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
  opacity: 0.4;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .pagination-controls {
    justify-content: center;
    gap: 1rem;
  }

  .pagination .page-link {
    padding: 0.5rem 0.7rem;
    min-width: 2.2rem;
    height: 2.2rem;
    font-size: 0.875rem;
  }
}


